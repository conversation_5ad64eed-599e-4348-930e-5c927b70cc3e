# Changelog

Toutes les modifications notables de ce projet seront documentées dans ce fichier.

Le format est basé sur [Keep a Changelog](https://keepachangelog.com/fr/1.0.0/),
et ce projet adhère au [Versioning Sémantique](https://semver.org/lang/fr/).

## [Non publié]

### Ajouté
- **Sécurisation automatique des logs** : Protection automatique des informations sensibles dans tous les logs
  - Masquage automatique des Personal Access Tokens (PAT)
  - Protection des API tokens classiques (format email:token)
  - Sécurisation des headers Authorization (Bearer et Basic)
  - Masquage des mots de passe dans les URLs
  - Protection des paramètres sensibles dans les URLs (token, password, secret, key)
  - Nettoyage des clés sensibles dans les structures JSON
- **Classe `SecurityFilter`** : Filtre de logging global pour la sécurisation
- **Méthode `_sanitize_error_message()`** dans `ConfluenceClient` pour nettoyer les messages d'erreur
- **Tests unitaires complets** : 11 tests pour valider la sécurisation des logs
- **Script de démonstration** : `demo_log_security.py` pour illustrer le fonctionnement
- **Documentation complète** :
  - `docs/securite_logs.md` : Guide détaillé sur la sécurité des logs
  - `docs/migration_securite_logs.md` : Guide de migration
  - Mise à jour du README principal avec section sécurité
- **Configuration** : Nouvelle option `SECURE_LOGGING` dans `.env` (activée par défaut)

### Modifié
- **Système de logging** : Intégration automatique du filtre de sécurité dans tous les handlers
- **Messages d'erreur** : Tous les messages d'erreur sont maintenant automatiquement nettoyés
- **Logging structuré** : Les champs extra dans les logs JSON sont également sécurisés
- **Documentation** : Mise à jour de la documentation de journalisation structurée

### Sécurité
- **Protection des credentials** : Aucun token ou mot de passe ne peut plus être exposé dans les logs
- **Conformité** : Respect des bonnes pratiques de sécurité pour la journalisation
- **Audit trail** : Préservation de la traçabilité sans compromettre la sécurité

## [1.0.0] - 2023-XX-XX

### Ajouté
- **Authentification PAT** : Support des Personal Access Tokens pour Confluence
- **Récupération de contenu** : Pages, blogs et pièces jointes depuis Confluence
- **Navigation hiérarchique** : Récupération récursive des pages enfants
- **Filtrage flexible** : Critères de recherche personnalisables via JSON
- **Téléchargements parallèles** : Téléchargement concurrent des pièces jointes
- **Stockage multiple** : Support du système de fichiers local et Google Cloud Storage
- **Détection des changements** : Suivi des modifications entre synchronisations
- **Journalisation structurée** : Logs JSON avec identifiants de corrélation
- **Circuit Breaker Pattern** : Protection contre les défaillances en cascade
- **Retry avec backoff exponentiel** : Gestion robuste des erreurs temporaires
- **Architecture modulaire** : Conception asynchrone pour les performances

### Fonctionnalités principales
- **Client API Confluence** : Interface complète pour l'API REST Confluence
- **Traitement des pièces jointes** : Support PDF, DOCX, XLSX, PPTX, TXT
- **Orchestrateur de synchronisation** : Gestion centralisée du processus
- **Suivi des changements** : Détection intelligente des modifications
- **Configuration flexible** : Variables d'environnement et fichiers JSON
- **Monitoring** : Application FastAPI pour le suivi en temps réel

### Documentation
- **Guide d'installation** : Instructions complètes d'installation et configuration
- **Documentation technique** : Architecture, modèles de données, API
- **Guides d'utilisation** : Exemples d'utilisation et cas d'usage
- **Tests** : Suite de tests unitaires et d'intégration

---

## Types de changements

- `Ajouté` pour les nouvelles fonctionnalités
- `Modifié` pour les changements dans les fonctionnalités existantes
- `Déprécié` pour les fonctionnalités qui seront supprimées dans les versions futures
- `Supprimé` pour les fonctionnalités supprimées dans cette version
- `Corrigé` pour les corrections de bugs
- `Sécurité` pour les vulnérabilités corrigées
