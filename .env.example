# Configuration Confluence
CONFLUENCE_URL=https://votre-instance.atlassian.net/wiki

# Méthode d'authentification 1: Personal Access Token (PAT) - Recommandée
CONFLUENCE_PAT_TOKEN=votre_token_pat

# Méthode d'authentification 2: API Token classique (obsolète mais toujours supportée)
# CONFLUENCE_USERNAME=<EMAIL>
# CONFLUENCE_API_TOKEN=votre_token_api

# Configuration de recherche
DEFAULT_SPACE_KEY=EXAMPLE
MAX_RESULTS=100
INCLUDE_ARCHIVED=false
CRITERIA_FILE_PATH=criteres_recherche.json
# CRITERIA_GCS_BUCKET=votre-bucket-gcs  # Optionnel: pour charger les critères depuis GCS
# Format alternatif: CRITERIA_FILE_PATH=gs://votre-bucket-gcs/chemin/vers/criteres_recherche.json

# Configuration de traitement
CHUNK_SIZE=1000
OVERLAP_SIZE=200
MAX_PARALLEL_DOWNLOADS=5
MAX_THREAD_WORKERS=5

# Configuration de stockage
STORAGE_TYPE=filesystem  # filesystem ou gcs
OUTPUT_DIR=output_data_dir
# GCS_BUCKET_NAME=votre-bucket-gcs  # Requis si STORAGE_TYPE=gcs
# GCS_BASE_PREFIX=confluence_rag
INCLUDE_ATTACHMENTS=true
ATTACHMENT_EXTENSIONS_TO_CONVERT=.pdf,.docx,.txt
MAX_ATTACHMENT_SIZE_MB=50

# Configuration de suivi
SYNC_REPORT_PATH=last_sync_report.json

# Configuration de retry
RETRY_MAX_ATTEMPTS=3
RETRY_INITIAL_BACKOFF=1.0
RETRY_MAX_BACKOFF=60.0
RETRY_BACKOFF_FACTOR=2.0
RETRY_JITTER=true
RETRY_STATUS_CODES=429,500,502,503,504

# Configuration du Circuit Breaker
CIRCUIT_BREAKER_ENABLED=true                # Activer/désactiver le Circuit Breaker
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5         # Nombre d'échecs consécutifs avant d'ouvrir le circuit
CIRCUIT_BREAKER_RESET_TIMEOUT=60.0          # Temps en secondes avant de passer en état semi-ouvert
CIRCUIT_BREAKER_RESET_THRESHOLD=2           # Nombre de succès consécutifs pour fermer le circuit

# Configuration de logging
LOG_LEVEL=INFO                               # Niveau de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_FILE=confluence_rag.log                  # Chemin du fichier de log
STRUCTURED_LOGGING=true                      # Utiliser le format JSON structuré pour les logs
SECURE_LOGGING=true                          # Activer la sécurisation automatique des logs (par défaut: true)